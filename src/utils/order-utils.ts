import { Timestamp } from 'firebase/firestore';

import type { CollectionEntity, OrderEntity } from '@/core.constants';
import { FREEZE_PERIOD_MS, OrderStatus, UserType } from '@/core.constants';

export interface TimerState {
  timeLeft: string;
  isFreezed: boolean;
}

export interface DeadlineInfo {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
  isExpired: boolean;
}

export function convertTimestampToDate(
  timestamp: Timestamp | Date | string,
): Date {
  if (timestamp instanceof Date) {
    return timestamp;
  }
  if (timestamp instanceof Timestamp) {
    return timestamp.toDate();
  }
  return new Date(timestamp);
}

export function calculateFreezeStatus(
  collection: CollectionEntity | null,
): boolean {
  if (!collection?.launchedAt) return false;

  const now = new Date();
  const launchedAt = convertTimestampToDate(collection.launchedAt);
  const freezeEndDate = new Date(launchedAt.getTime() + FREEZE_PERIOD_MS);

  return now < freezeEndDate;
}

export function calculateDeadlineInfo(
  deadline: Timestamp | Date | string,
): DeadlineInfo {
  const now = new Date();
  const deadlineDate = convertTimestampToDate(deadline);
  const timeDiff = deadlineDate.getTime() - now.getTime();

  if (timeDiff <= 0) {
    return { days: 0, hours: 0, minutes: 0, seconds: 0, isExpired: true };
  }

  const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
  const hours = Math.floor(
    (timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
  );
  const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

  return { days, hours, minutes, seconds, isExpired: false };
}

export function formatTimeLeft(deadlineInfo: DeadlineInfo): string {
  if (deadlineInfo.isExpired) return 'Expired';

  const { days, hours, minutes, seconds } = deadlineInfo;

  if (days > 0) {
    return `${days}d ${hours}h ${minutes}m ${seconds}s`;
  }
  if (hours > 0) {
    return `${hours}h ${minutes}m ${seconds}s`;
  }
  if (minutes > 0) {
    return `${minutes}m ${seconds}s`;
  }
  return `${seconds}s`;
}

export function shouldShowDeadlineTimer(order: OrderEntity): boolean {
  return Boolean(
    order.deadline &&
      (order.status === OrderStatus.PAID ||
        order.status === OrderStatus.GIFT_SENT_TO_RELAYER),
  );
}

export function formatOrderStatus(status: OrderStatus): string {
  if (status === OrderStatus.GIFT_SENT_TO_RELAYER) {
    return 'Gift Sent';
  }

  const formatted = status.replace(/_/g, ' ');
  return formatted.charAt(0).toUpperCase() + formatted.slice(1);
}

export function getOrderDisplayNumber(order: OrderEntity): string {
  return order.number ? `#${order.number}` : `#${order.id?.slice(-6)}`;
}

export function canCancelOrder(order: OrderEntity): boolean {
  return [
    OrderStatus.PAID,
    OrderStatus.ACTIVE,
    OrderStatus.GIFT_SENT_TO_RELAYER,
  ].includes(order.status);
}

export function canCreateSecondaryMarketOrder(
  order: OrderEntity,
  currentUserId?: string,
): boolean {
  return (
    order.status === OrderStatus.PAID &&
    currentUserId === order.buyerId &&
    Boolean(order.sellerId && order.buyerId)
  );
}

export function hasSecondaryMarketPrice(order: OrderEntity): boolean {
  return Boolean(order.secondaryMarketPrice && order.secondaryMarketPrice > 0);
}

export function getOtherUserId(
  order: OrderEntity,
  userRole: UserType,
): string | undefined {
  return userRole === UserType.SELLER ? order.buyerId : order.sellerId;
}

export function shouldShowUserInfo(order: OrderEntity): boolean {
  return [OrderStatus.PAID, OrderStatus.GIFT_SENT_TO_RELAYER].includes(
    order.status,
  );
}

export function shouldShowFreezeWarning(
  order: OrderEntity,
  userType: UserType,
  isFreezed: boolean,
): boolean {
  return (
    isFreezed &&
    userType === UserType.SELLER &&
    order.status === OrderStatus.PAID
  );
}

export function shouldShowGiftReadySection(
  order: OrderEntity,
  userRole: UserType,
): boolean {
  return (
    order.status === OrderStatus.GIFT_SENT_TO_RELAYER &&
    userRole === UserType.BUYER
  );
}

export function shouldShowGiftRefundSection(
  order: OrderEntity,
  userRole: UserType,
): boolean {
  return (
    order.status === OrderStatus.CANCELLED &&
    Boolean(order.owned_gift_id) &&
    userRole === UserType.SELLER
  );
}
