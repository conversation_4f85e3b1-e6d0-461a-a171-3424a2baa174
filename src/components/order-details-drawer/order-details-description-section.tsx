import { Caption } from '@telegram-apps/telegram-ui';

import type { CollectionEntity } from '@/core.constants';

interface OrderDetailsDescriptionSectionProps {
  collection: CollectionEntity | null;
}

export function OrderDetailsDescriptionSection({
  collection,
}: OrderDetailsDescriptionSectionProps) {
  if (!collection?.description) return null;

  return (
    <div className="text-center">
      <Caption level="2" weight="3" className="text-[#708499]">
        {collection.description}
      </Caption>
    </div>
  );
}
