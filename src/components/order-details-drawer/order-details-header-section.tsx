import { Caption } from '@telegram-apps/telegram-ui';

import type { CollectionEntity, OrderEntity } from '@/core.constants';

interface OrderDetailsHeaderSectionProps {
  order: OrderEntity;
  collection: CollectionEntity | null;
}

export function OrderDetailsHeaderSection({
  order,
  collection,
}: OrderDetailsHeaderSectionProps) {
  return (
    <div className="text-center space-y-2">
      <h1 className="text-2xl font-bold text-[#f5f5f5]">
        {collection?.name || 'Unknown Collection'}
      </h1>
      <Caption level="1" weight="3" className="text-[#708499]">
        #
        {order.number ||
          (typeof order.id === 'string' ? order.id?.slice(-6) : 'N/A')}
      </Caption>
    </div>
  );
}
