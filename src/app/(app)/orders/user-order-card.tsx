'use client';

import { Caption } from '@telegram-apps/telegram-ui';
import { Timestamp } from 'firebase/firestore';
import {
  AlertTriangle,
  CheckCircle,
  Clock,
  Gift,
  User,
  XCircle,
} from 'lucide-react';
import { useEffect, useState } from 'react';

import { TgsOrImage } from '@/components/TgsOrImage';
import { Card, CardContent } from '@/components/ui/card';
import type { OrderEntity } from '@/core.constants';
import { UserType } from '@/core.constants';
import { OrderStatus } from '@/core.constants';
import { useRootContext } from '@/root-context';

interface UserOrderCardProps {
  order: OrderEntity;
  userType: UserType;
  onClick: () => void;
}

export function UserOrderCard({
  order,
  userType,
  onClick,
}: UserOrderCardProps) {
  const { collections } = useRootContext();
  const [timeLeft, setTimeLeft] = useState<string>('');
  const [isFreezed, setIsFreezed] = useState<boolean>(false);

  const collection = collections.find((c) => c.id === order.collectionId);

  useEffect(() => {
    const updateTimers = () => {
      const now = new Date();

      // Check freeze period (launchedAt + 21 days)
      if (collection?.launchedAt) {
        const launchedAt =
          collection.launchedAt instanceof Timestamp
            ? collection.launchedAt.toDate()
            : new Date(collection.launchedAt);
        const freezeEndDate = new Date(
          launchedAt.getTime() + 21 * 24 * 60 * 60 * 1000,
        );
        setIsFreezed(now < freezeEndDate);
      }

      if (
        order.deadline &&
        (order.status === OrderStatus.PAID ||
          order.status === OrderStatus.GIFT_SENT_TO_RELAYER)
      ) {
        const deadline =
          order.deadline instanceof Timestamp
            ? order.deadline.toDate()
            : new Date(order.deadline);
        const timeDiff = deadline.getTime() - now.getTime();

        if (timeDiff > 0) {
          const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
          const hours = Math.floor(
            (timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
          );
          const minutes = Math.floor(
            (timeDiff % (1000 * 60 * 60)) / (1000 * 60),
          );
          const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

          if (days > 0) {
            setTimeLeft(`${days}d ${hours}h ${minutes}m ${seconds}s`);
          } else if (hours > 0) {
            setTimeLeft(`${hours}h ${minutes}m ${seconds}s`);
          } else if (minutes > 0) {
            setTimeLeft(`${minutes}m ${seconds}s`);
          } else {
            setTimeLeft(`${seconds}s`);
          }
        } else {
          setTimeLeft('Expired');
        }
      } else {
        setTimeLeft('');
      }
    };

    updateTimers();
    const interval = setInterval(updateTimers, 1000); // Update every second

    return () => clearInterval(interval);
  }, [order.deadline, order.status, collection?.launchedAt]);

  const getStatusIcon = () => {
    switch (order.status) {
      case OrderStatus.ACTIVE:
        return <Clock className="w-4 h-4 text-blue-400" />;
      case OrderStatus.PAID:
        return <AlertTriangle className="w-4 h-4 text-yellow-400" />;
      case OrderStatus.GIFT_SENT_TO_RELAYER:
        return <Gift className="w-4 h-4 text-purple-400" />;
      case OrderStatus.FULFILLED:
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      case OrderStatus.CANCELLED:
        return <XCircle className="w-4 h-4 text-red-400" />;
      default:
        return null;
    }
  };

  const getOrderStatus = () => {
    if (order.status === OrderStatus.GIFT_SENT_TO_RELAYER) {
      return 'Sent to bot';
    }

    const formatted = order.status.replace(/_/g, ' ');
    return formatted.charAt(0).toUpperCase() + formatted.slice(1);
  };

  return (
    <Card
      className="bg-[#232e3c] border-[#3a4a5c] hover:bg-[#2a3441] transition-colors cursor-pointer group"
      onClick={onClick}
    >
      <CardContent className="p-2 flex flex-col h-full">
        <div className="pt-6 relative rounded-lg overflow-hidden bg-[#17212b] mb-1 aspect-[1/1.2]">
          {collection ? (
            <TgsOrImage
              isImage={false}
              collectionId={collection.id}
              imageProps={{
                alt: collection.name || 'Order item',
                fill: true,
                className:
                  'object-cover pt-4 group-hover:scale-105 transition-transform duration-200',
              }}
              tgsProps={{
                style: { height: 'auto', width: 'auto', padding: '8px' },
              }}
            />
          ) : (
            <div className="w-full h-full bg-[#3a4a5c] rounded flex items-center justify-center">
              <div className="w-6 h-6 bg-[#17212b] rounded" />
            </div>
          )}

          <div className="flex w-full items-center justify-between mb-1 absolute top-0 left-0 px-2 pt-1.5">
            <div className="flex items-center gap-1">
              {getStatusIcon()}
              <span className="text-[10px] text-[#708499]">
                {getOrderStatus()}
              </span>
            </div>
            <div className="flex items-center gap-1">
              <User className="w-2.5 h-2.5 text-[#6ab2f2]" />
              <span className="text-[10px] text-[#6ab2f2] capitalize">
                {userType}
              </span>
            </div>
          </div>
        </div>

        <div className={'flex items-center justify-between mb-1'}>
          <Caption level="1" weight="1" className="truncate">
            {collection?.name || 'Unknown Collection'}
          </Caption>
          <Caption level="2" weight="3" className="w-fit text-[#78797e]">
            #
            {order.number ||
              (typeof order.id === 'string' ? order.id?.slice(-6) : 'N/A')}
          </Caption>
        </div>

        {(order.status === OrderStatus.PAID ||
          order.status === OrderStatus.GIFT_SENT_TO_RELAYER) && (
          <div
            className={`p-1.5 bg-orange-500/10 border border-orange-500/20 rounded text-[10px] ${
              order.status === OrderStatus.PAID &&
              isFreezed &&
              userType === UserType.SELLER
                ? 'mb-1'
                : ''
            }`}
          >
            {timeLeft ? (
              <>
                <div className="flex items-center gap-1 mb-0.5">
                  <Clock className="w-2.5 h-2.5 text-orange-400" />
                  <span className="text-orange-400 font-medium">Deadline</span>
                </div>
                <div className="text-[#f5f5f5] font-mono text-xs">
                  {timeLeft}
                </div>
                <div className="text-[#708499] text-[9px]">
                  {order.status === OrderStatus.PAID &&
                    userType === UserType.SELLER &&
                    'Send or lose collateral'}
                  {order.status === OrderStatus.PAID &&
                    userType === UserType.BUYER &&
                    'Seller must send'}
                  {order.status === OrderStatus.GIFT_SENT_TO_RELAYER &&
                    userType === UserType.BUYER &&
                    'Claim or lose collateral'}
                  {order.status === OrderStatus.GIFT_SENT_TO_RELAYER &&
                    userType === UserType.SELLER &&
                    'Buyer must claim'}
                </div>
              </>
            ) : (
              <>
                <div className="flex items-center gap-1 mb-0.5">
                  <Clock className="w-2.5 h-2.5 text-orange-400" />
                  <span className="text-orange-400 font-medium">Waiting</span>
                </div>
                <div className="text-[#708499] text-[9px] mt-[18px]">
                  Gift will become transferable soon
                </div>
              </>
            )}
          </div>
        )}

        {order.status === OrderStatus.PAID &&
          isFreezed &&
          userType === UserType.SELLER && (
            <div className="p-1.5 bg-yellow-500/10 border border-yellow-500/20 rounded text-[10px]">
              <div className="flex items-center gap-1">
                <AlertTriangle className="w-2.5 h-2.5 text-yellow-400" />
                <span className="text-yellow-400">Freeze period</span>
              </div>
            </div>
          )}
      </CardContent>
    </Card>
  );
}
