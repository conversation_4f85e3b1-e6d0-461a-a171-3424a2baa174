import { Timestamp } from 'firebase/firestore';

import type { OrderEntity, UserType } from '@/core.constants';
import { OrderStatus } from '@/core.constants';
import { getOrderDisplayNumber } from '@/utils/order-utils';

interface DetailRowProps {
  label: string;
  value: string;
  isLast?: boolean;
}

function DetailRow({ label, value, isLast = false }: DetailRowProps) {
  return (
    <div
      className={`flex justify-between items-center py-2 ${
        !isLast ? 'border-b border-[#3a4a5c]/30' : ''
      }`}
    >
      <span className="text-[#f5f5f5] font-medium">{label}</span>
      <span className="text-[#6ab2f2] font-semibold capitalize">{value}</span>
    </div>
  );
}

interface UserOrderDetailsSectionProps {
  order: OrderEntity;
  userType: UserType;
}

export function UserOrderDetailsSection({
  order,
  userType,
}: UserOrderDetailsSectionProps) {
  const getStatusDisplay = () => {
    return order.status === OrderStatus.GIFT_SENT_TO_RELAYER
      ? 'Gift Sent'
      : order.status.replace('_', ' ');
  };

  const getCreatedAtDisplay = () => {
    if (!order.createdAt) return null;
    return (
      order.createdAt instanceof Timestamp
        ? order.createdAt.toDate()
        : new Date(order.createdAt)
    ).toLocaleDateString();
  };

  const details = [
    { label: 'Order Number', value: getOrderDisplayNumber(order) },
    { label: 'Your Role', value: userType },
    { label: 'Status', value: getStatusDisplay() },
    ...(order.createdAt
      ? [{ label: 'Created', value: getCreatedAtDisplay()! }]
      : []),
  ];

  return (
    <div className="space-y-3">
      {details.map((detail, index) => (
        <DetailRow
          key={detail.label}
          label={detail.label}
          value={detail.value}
          isLast={index === details.length - 1}
        />
      ))}
    </div>
  );
}
