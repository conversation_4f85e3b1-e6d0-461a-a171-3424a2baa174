import { Button } from '@/components/ui/button';
import type { OrderEntity } from '@/core.constants';
import {
  canCancelOrder,
  canCreateSecondaryMarketOrder,
  hasSecondaryMarketPrice,
} from '@/utils/order-utils';

interface UserOrderActionsSectionProps {
  order: OrderEntity;
  currentUserId?: string;
  onCancelOrder: () => void;
  onCreateSecondaryMarketOrder: () => void;
}

export function UserOrderActionsSection({
  order,
  currentUserId,
  onCancelOrder,
  onCreateSecondaryMarketOrder,
}: UserOrderActionsSectionProps) {
  const canCancel = canCancelOrder(order);
  const canCreateSecondary = canCreateSecondaryMarketOrder(
    order,
    currentUserId,
  );
  const hasSecondaryPrice = hasSecondaryMarketPrice(order);

  if (!canCancel && !canCreateSecondary) {
    return null;
  }

  return (
    <div className="pt-6 border-t border-[#3a4a5c]/30 space-y-3">
      {canCreateSecondary && (
        <Button
          onClick={onCreateSecondaryMarketOrder}
          className="w-full rounded-xl py-3 font-semibold bg-[#6ab2f2] hover:bg-[#5a9fd9] text-white"
        >
          {hasSecondaryPrice ? 'Update Resale Order' : 'Create Resale Order'}
        </Button>
      )}

      {canCancel && (
        <Button
          variant="destructive"
          onClick={onCancelOrder}
          className="w-full rounded-xl py-3 font-semibold"
        >
          Cancel Order
        </Button>
      )}
    </div>
  );
}
