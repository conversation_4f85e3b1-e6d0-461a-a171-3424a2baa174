'use client';

import { Repeat, ShoppingCart, TrendingUp, User } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

import { AppRoutes } from '@/core.constants';

export default function RootLayoutFooter() {
  const pathname = usePathname();

  const navItems = [
    {
      icon: TrendingUp,
      label: 'Marketplace',
      route: AppRoutes.MARKETPLACE,
      active: pathname === AppRoutes.MARKETPLACE,
    },
    {
      icon: Repeat,
      label: 'Secondary market',
      route: AppRoutes.SECONDARY_MARKET,
      active: pathname === AppRoutes.SECONDARY_MARKET,
    },
    {
      icon: ShoppingCart,
      label: 'Orders',
      route: AppRoutes.ORDERS,
      active: pathname === AppRoutes.ORDERS,
    },
    {
      icon: User,
      label: 'Profile',
      route: AppRoutes.PROFILE,
      active: pathname === AppRoutes.PROFILE,
    },
  ];

  return (
    <footer className="fixed bottom-0 left-0 right-0 bg-[#17212b] text-[#f5f5f5] border-t border-[#3a4a5c] z-50 h-16">
      <div className="grid grid-cols-4 items-center h-full">
        {navItems.map((item) => {
          const IconComponent = item.icon;
          return (
            <Link
              key={item.route}
              href={item.route}
              className={`flex flex-col items-center gap-1 py-3 h-auto transition-colors ${
                item.active
                  ? 'text-[#6ab2f2]'
                  : 'text-[#708499] hover:text-[#f5f5f5]'
              }`}
            >
              <IconComponent className="w-5 h-5" />
              <span className="text-[10px] font-medium">{item.label}</span>
            </Link>
          );
        })}
      </div>
    </footer>
  );
}
